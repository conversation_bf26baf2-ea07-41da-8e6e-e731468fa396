/* Performance Optimized CSS for My Finance App */
/* এই CSS ফাইল অ্যাপের পারফরমেন্স উন্নত করার জন্য */

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* Reduce animations for better performance */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* CSS Containment for better performance */
.dashboard-grid {
  contain: layout style paint;
}

.recent-transactions-section {
  contain: layout style paint;
}

.chart-card {
  contain: layout style paint;
}

.notification-panel {
  contain: layout style paint;
}

.feature-card {
  contain: layout style paint;
}

/* Hardware acceleration for smooth animations */
.loader-animation,
.wallet-icon,
.loading-dots,
.particle,
.shimmer,
.notification-bell,
.theme-toggle,
.balance-toggle {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimize heavy animations */
.particle {
  animation-duration: 8s; /* Increased from 6s for smoother performance */
  animation-timing-function: ease-out; /* Changed from ease-in-out */
}

/* Reduce particle count on mobile */
@media (max-width: 768px) {
  .particle:nth-child(n+5) {
    display: none;
  }
}

/* Optimize shimmer effect */
.shimmer::before {
  animation-duration: 4s; /* Increased from 3s */
  animation-timing-function: ease-out;
}

/* Optimize loading dots */
.dot {
  animation-duration: 1.8s; /* Increased from 1.4s */
}

/* Optimize glow animations */
.developer-name {
  animation-duration: 3s; /* Increased from 2s */
}

/* Reduce box-shadow complexity */
.header {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* Simplified */
}

.header.scrolled {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* Simplified */
}

/* Optimize button hover effects */
.btn:hover {
  transform: translateY(-1px); /* Reduced from -2px */
  transition: transform 0.2s ease; /* Faster transition */
}

/* Optimize card hover effects */
.stat-card:hover,
.income-stat-card:hover,
.expense-stat-card:hover,
.loan-stat-card:hover,
.bank-stat-card:hover {
  transform: translateY(-2px) scale(1.01); /* Reduced scale */
  transition: transform 0.2s ease; /* Faster transition */
}

/* Optimize notification animations */
.notification-bell.has-notifications {
  animation-duration: 1s; /* Increased from 0.8s */
}

.notification-item {
  transition: transform 0.2s ease; /* Faster transition */
}

.notification-item:hover {
  transform: translateY(-1px); /* Reduced from -2px */
}

/* Optimize modal animations */
.modal-content {
  animation-duration: 0.3s; /* Reduced from 0.4s */
}

/* Optimize search result animations */
.search-result-item {
  animation-duration: 0.3s; /* Reduced from 0.5s */
}

.search-result-item:hover {
  transform: translateX(5px) scale(1.01); /* Reduced movement and scale */
}

/* Optimize chart container */
.chart-container {
  contain: layout style paint;
  will-change: auto; /* Only when needed */
}

/* Optimize progress bars */
.progress-fill {
  transition: width 0.3s ease; /* Reduced from 0.5s */
}

/* Optimize category items */
.category-item:hover {
  transform: translateY(-2px); /* Reduced from -5px */
  transition: transform 0.2s ease;
}

/* Optimize top category items */
.top-category-item:hover {
  transform: translateX(4px); /* Reduced from 8px */
  transition: transform 0.2s ease;
}

/* Optimize reminder items */
.reminder-item:hover {
  transform: translateX(2px); /* Reduced from 5px */
  transition: transform 0.2s ease;
}

/* Optimize quick buttons */
.quick-btn:hover {
  transform: translateY(-1px); /* Reduced from -2px */
  transition: transform 0.2s ease;
}

/* Optimize tab animations */
.notes-tab:not(.active):hover {
  transform: translateY(-1px); /* Reduced from -2px */
  transition: transform 0.2s ease;
}

/* Optimize image hover effects */
.notes-editor img:hover {
  transform: scale(1.01); /* Reduced from 1.02 */
  transition: transform 0.2s ease;
}

/* Optimize resize handles */
.resize-handle:hover {
  transform: scale(1.1); /* Reduced from 1.2 */
  transition: transform 0.2s ease;
}

/* Optimize balance animations */
.current-balance {
  transition: all 0.3s ease; /* Reduced from 0.4s */
}

/* Optimize export button animations */
.export-buttons .btn:hover {
  transform: translateY(-2px); /* Reduced from -3px */
  transition: transform 0.2s ease;
}

/* Optimize filter animations */
.category-filter-btn:hover,
.date-filter-btn:hover {
  transform: translateY(-1px); /* Reduced from -2px */
  transition: transform 0.2s ease;
}

/* Optimize suggestion tags */
.suggestion-tag:hover {
  transform: translateY(-2px) scale(1.02); /* Reduced movement and scale */
  transition: transform 0.2s ease;
}

/* Optimize notification alert animations */
.notification-alert-btn:hover {
  transform: translateY(-0.5px); /* Reduced from -1px */
  transition: transform 0.2s ease;
}

/* Optimize summary card animations */
.summary-card:hover {
  transform: translateY(-3px); /* Reduced from -5px */
  transition: transform 0.2s ease;
}

/* Optimize chart control animations */
.chart-controls .btn:hover {
  transform: scale(1.02); /* Reduced from 1.05 */
  transition: transform 0.2s ease;
}

/* Disable expensive animations on mobile */
@media (max-width: 768px) {
  .particle,
  .shimmer::before,
  .developer-name,
  .wallet-icon {
    animation: none !important;
  }
  
  .loader-animation {
    animation: none !important;
  }
  
  .loading-dots .dot {
    animation: none !important;
  }
  
  /* Simplify hover effects on mobile */
  .btn:hover,
  .stat-card:hover,
  .notification-item:hover,
  .search-result-item:hover {
    transform: none !important;
  }
}

/* Optimize font loading */
@font-display: swap;

/* Optimize background patterns */
.pattern-dots,
.pattern-grid,
.pattern-diagonal,
.pattern-waves,
.pattern-hexagon,
.pattern-stripes,
.pattern-zigzag,
.pattern-circles,
.pattern-triangles,
.pattern-crosshatch,
.pattern-stars {
  background-size: 20px 20px; /* Smaller pattern size for better performance */
}

/* Optimize backdrop filters */
.header,
.notification-panel,
.modal-content {
  backdrop-filter: blur(5px); /* Reduced from 10px+ */
}

/* Optimize gradient animations */
.income-stat-card::before,
.expense-stat-card::before,
.loan-stat-card::before,
.bank-stat-card::before {
  transition: left 0.3s ease; /* Reduced from 0.5s */
}

/* Optimize pulse animations */
@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02); /* Reduced from 1.05 */
  }
}

/* Optimize spin animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Optimize fade animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Optimize slide animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px); /* Reduced from 30px */
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px); /* Reduced from -30px */
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Optimize scale animations */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9); /* Reduced from 0.8 */
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Performance-focused utility classes */
.no-animation {
  animation: none !important;
  transition: none !important;
}

.gpu-accelerated {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.smooth-scroll {
  scroll-behavior: smooth;
}

/* Optimize for 60fps */
.smooth-transition {
  transition-duration: 0.16s; /* 1/60th of a second */
  transition-timing-function: ease-out;
}

/* Reduce repaints and reflows */
.layout-stable {
  contain: layout;
}

.paint-stable {
  contain: paint;
}

.style-stable {
  contain: style;
}

.full-containment {
  contain: layout style paint;
}
