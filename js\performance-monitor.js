// Performance Monitor for My Finance App
// এই ফাইল অ্যাপের পারফরমেন্স মনিটর করার জন্য

class PerformanceMonitor {
    constructor() {
        this.metrics = {
            loadTime: 0,
            domContentLoaded: 0,
            firstPaint: 0,
            firstContentfulPaint: 0,
            largestContentfulPaint: 0,
            cumulativeLayoutShift: 0,
            firstInputDelay: 0,
            memoryUsage: 0,
            slowOperations: [],
            resourceTimings: []
        };
        
        this.observers = [];
        this.isMonitoring = false;
        
        this.init();
    }

    init() {
        console.log('📊 Performance Monitor initialized');
        this.startMonitoring();
        this.setupObservers();
        this.monitorMemoryUsage();
        this.trackSlowOperations();
    }

    startMonitoring() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        
        // Monitor page load metrics
        window.addEventListener('load', () => {
            this.collectLoadMetrics();
        });

        // Monitor DOM content loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.collectDOMMetrics();
            });
        } else {
            this.collectDOMMetrics();
        }

        console.log('🎯 Performance monitoring started');
    }

    collectLoadMetrics() {
        if (!('performance' in window)) return;

        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
            this.metrics.loadTime = navigation.loadEventEnd - navigation.loadEventStart;
            this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
        }

        // Collect paint metrics
        const paintEntries = performance.getEntriesByType('paint');
        paintEntries.forEach(entry => {
            if (entry.name === 'first-paint') {
                this.metrics.firstPaint = entry.startTime;
            } else if (entry.name === 'first-contentful-paint') {
                this.metrics.firstContentfulPaint = entry.startTime;
            }
        });

        // Collect resource timings
        this.collectResourceTimings();
        
        console.log('📈 Load metrics collected:', this.metrics);
    }

    collectDOMMetrics() {
        const domElements = document.querySelectorAll('*').length;
        const domDepth = this.calculateDOMDepth();
        
        console.log(`📋 DOM Metrics: ${domElements} elements, depth: ${domDepth}`);
    }

    calculateDOMDepth() {
        let maxDepth = 0;
        
        function getDepth(element, depth = 0) {
            maxDepth = Math.max(maxDepth, depth);
            for (let child of element.children) {
                getDepth(child, depth + 1);
            }
        }
        
        getDepth(document.body);
        return maxDepth;
    }

    collectResourceTimings() {
        const resources = performance.getEntriesByType('resource');
        
        this.metrics.resourceTimings = resources.map(resource => ({
            name: resource.name,
            duration: resource.duration,
            size: resource.transferSize || 0,
            type: this.getResourceType(resource.name)
        }));

        // Identify slow resources
        const slowResources = resources.filter(resource => resource.duration > 1000);
        if (slowResources.length > 0) {
            console.warn('⚠️ Slow resources detected:', slowResources);
        }
    }

    getResourceType(url) {
        if (url.includes('.css')) return 'CSS';
        if (url.includes('.js')) return 'JavaScript';
        if (url.includes('.png') || url.includes('.jpg') || url.includes('.gif') || url.includes('.svg')) return 'Image';
        if (url.includes('fonts')) return 'Font';
        return 'Other';
    }

    setupObservers() {
        // Largest Contentful Paint Observer
        if ('PerformanceObserver' in window) {
            try {
                const lcpObserver = new PerformanceObserver((entryList) => {
                    const entries = entryList.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    this.metrics.largestContentfulPaint = lastEntry.startTime;
                });
                lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
                this.observers.push(lcpObserver);
            } catch (e) {
                console.warn('LCP observer not supported');
            }

            // Cumulative Layout Shift Observer
            try {
                let clsValue = 0;
                const clsObserver = new PerformanceObserver((entryList) => {
                    for (const entry of entryList.getEntries()) {
                        if (!entry.hadRecentInput) {
                            clsValue += entry.value;
                        }
                    }
                    this.metrics.cumulativeLayoutShift = clsValue;
                });
                clsObserver.observe({ entryTypes: ['layout-shift'] });
                this.observers.push(clsObserver);
            } catch (e) {
                console.warn('CLS observer not supported');
            }

            // First Input Delay Observer
            try {
                const fidObserver = new PerformanceObserver((entryList) => {
                    for (const entry of entryList.getEntries()) {
                        this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
                    }
                });
                fidObserver.observe({ entryTypes: ['first-input'] });
                this.observers.push(fidObserver);
            } catch (e) {
                console.warn('FID observer not supported');
            }
        }
    }

    monitorMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                this.metrics.memoryUsage = {
                    used: Math.round(memory.usedJSHeapSize / 1048576), // MB
                    total: Math.round(memory.totalJSHeapSize / 1048576), // MB
                    limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
                };

                // Warn if memory usage is high
                const usagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
                if (usagePercent > 80) {
                    console.warn(`⚠️ High memory usage: ${usagePercent.toFixed(1)}%`);
                }
            }, 30000); // Check every 30 seconds
        }
    }

    trackSlowOperations() {
        // Override setTimeout and setInterval to track long-running operations
        const originalSetTimeout = window.setTimeout;
        const originalSetInterval = window.setInterval;

        window.setTimeout = (callback, delay, ...args) => {
            const start = performance.now();
            return originalSetTimeout(() => {
                const duration = performance.now() - start;
                if (duration > 100) { // Log operations taking more than 100ms
                    this.metrics.slowOperations.push({
                        type: 'setTimeout',
                        duration,
                        delay,
                        timestamp: Date.now()
                    });
                }
                callback.apply(this, args);
            }, delay);
        };

        window.setInterval = (callback, delay, ...args) => {
            return originalSetInterval(() => {
                const start = performance.now();
                callback.apply(this, args);
                const duration = performance.now() - start;
                if (duration > 50) { // Log interval callbacks taking more than 50ms
                    this.metrics.slowOperations.push({
                        type: 'setInterval',
                        duration,
                        delay,
                        timestamp: Date.now()
                    });
                }
            }, delay);
        };
    }

    // Get current performance metrics
    getMetrics() {
        return { ...this.metrics };
    }

    // Get performance score (0-100)
    getPerformanceScore() {
        let score = 100;

        // Deduct points for slow metrics
        if (this.metrics.firstContentfulPaint > 2000) score -= 20;
        if (this.metrics.largestContentfulPaint > 4000) score -= 20;
        if (this.metrics.cumulativeLayoutShift > 0.1) score -= 15;
        if (this.metrics.firstInputDelay > 100) score -= 15;
        if (this.metrics.loadTime > 3000) score -= 10;

        // Deduct points for slow operations
        const recentSlowOps = this.metrics.slowOperations.filter(
            op => Date.now() - op.timestamp < 60000 // Last minute
        );
        score -= Math.min(recentSlowOps.length * 2, 20);

        return Math.max(score, 0);
    }

    // Generate performance report
    generateReport() {
        const score = this.getPerformanceScore();
        const report = {
            score,
            grade: this.getGrade(score),
            metrics: this.getMetrics(),
            recommendations: this.getRecommendations()
        };

        console.log('📊 Performance Report:', report);
        return report;
    }

    getGrade(score) {
        if (score >= 90) return 'A';
        if (score >= 80) return 'B';
        if (score >= 70) return 'C';
        if (score >= 60) return 'D';
        return 'F';
    }

    getRecommendations() {
        const recommendations = [];

        if (this.metrics.firstContentfulPaint > 2000) {
            recommendations.push('Optimize critical rendering path');
        }
        if (this.metrics.largestContentfulPaint > 4000) {
            recommendations.push('Optimize largest contentful paint');
        }
        if (this.metrics.cumulativeLayoutShift > 0.1) {
            recommendations.push('Reduce layout shifts');
        }
        if (this.metrics.firstInputDelay > 100) {
            recommendations.push('Optimize JavaScript execution');
        }
        if (this.metrics.slowOperations.length > 10) {
            recommendations.push('Optimize slow operations');
        }

        // Check resource recommendations
        const largeResources = this.metrics.resourceTimings.filter(r => r.size > 1000000); // > 1MB
        if (largeResources.length > 0) {
            recommendations.push('Optimize large resources');
        }

        return recommendations;
    }

    // Display performance widget
    showPerformanceWidget() {
        const widget = document.createElement('div');
        widget.id = 'performance-widget';
        widget.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            min-width: 200px;
            cursor: pointer;
        `;

        this.updateWidget(widget);
        document.body.appendChild(widget);

        // Update widget every 5 seconds
        setInterval(() => {
            this.updateWidget(widget);
        }, 5000);

        // Click to toggle detailed view
        widget.addEventListener('click', () => {
            console.log(this.generateReport());
        });
    }

    updateWidget(widget) {
        const score = this.getPerformanceScore();
        const memory = this.metrics.memoryUsage;
        
        widget.innerHTML = `
            <div>Performance: ${score}/100</div>
            <div>Grade: ${this.getGrade(score)}</div>
            ${memory ? `<div>Memory: ${memory.used}MB</div>` : ''}
            <div>Slow Ops: ${this.metrics.slowOperations.length}</div>
            <small>Click for details</small>
        `;

        // Color based on score
        const color = score >= 80 ? '#4CAF50' : score >= 60 ? '#FF9800' : '#F44336';
        widget.style.borderLeft = `4px solid ${color}`;
    }

    // Stop monitoring
    stopMonitoring() {
        this.isMonitoring = false;
        this.observers.forEach(observer => observer.disconnect());
        this.observers = [];
        console.log('📊 Performance monitoring stopped');
    }

    // Cleanup
    cleanup() {
        this.stopMonitoring();
        const widget = document.getElementById('performance-widget');
        if (widget) {
            widget.remove();
        }
    }
}

// Initialize Performance Monitor
window.performanceMonitor = new PerformanceMonitor();

// Show performance widget in development
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    setTimeout(() => {
        window.performanceMonitor.showPerformanceWidget();
    }, 2000);
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    window.performanceMonitor.cleanup();
});
