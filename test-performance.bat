@echo off
echo ========================================
echo    My Finance App Performance Test
echo ========================================
echo.

echo 🚀 Starting performance test...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Node.js is not installed!
    echo Please install Node.js from https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js found
echo.

REM Check if dependencies are installed
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
    if errorlevel 1 (
        echo ❌ Error: Failed to install dependencies!
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed
    echo.
)

echo 📊 Running performance test...
echo.

REM Start the app with performance monitoring
echo Starting Electron app with performance monitoring...
echo Press Ctrl+C to stop the test
echo.

npm run dev -- --trace-gc --prof

echo.
echo 📈 Performance test completed!
echo Check the console output for performance metrics.
echo.
pause
