/**
 * Simple Font Loader - Handles Bengali font loading safely
 * Avoids MIME type errors and provides reliable fallbacks
 */

(function() {
    'use strict';
    
    // Font loading configuration
    const FONT_CONFIG = {
        timeout: 3000, // 3 seconds timeout
        retryDelay: 1000, // 1 second retry delay
        maxRetries: 2
    };
    
    // Font sources in order of preference
    const FONT_SOURCES = [
        {
            name: '<PERSON><PERSON><PERSON><PERSON>',
            url: 'https://fonts.bunny.net/css2?family=Kalpurush:wght@400;500;600;700&display=swap',
            type: 'primary'
        },
        {
            name: 'Noto Sans Bengali',
            url: 'https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@300;400;500;600;700&display=swap',
            type: 'fallback'
        }
    ];
    
    // System fonts that are likely to be available
    const SYSTEM_FONTS = ['SolaimanLipi', 'Nikosh', 'Vrinda', 'Shonar Bangla', 'Bangla'];
    
    let retryCount = 0;
    let fontsLoaded = false;
    
    /**
     * Load a font CSS file safely
     */
    function loadFontCSS(fontSource) {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = fontSource.url;
            link.type = 'text/css';
            link.media = 'all';
            
            // Set up success handler
            link.onload = () => {
                console.log(`✅ Font loaded successfully: ${fontSource.name}`);
                resolve(fontSource);
            };
            
            // Set up error handler
            link.onerror = () => {
                console.warn(`❌ Failed to load font: ${fontSource.name}`);
                reject(new Error(`Failed to load ${fontSource.name}`));
            };
            
            // Add timeout
            setTimeout(() => {
                if (!link.sheet) {
                    console.warn(`⏰ Font loading timeout: ${fontSource.name}`);
                    reject(new Error(`Timeout loading ${fontSource.name}`));
                }
            }, FONT_CONFIG.timeout);
            
            // Append to head
            document.head.appendChild(link);
        });
    }
    
    /**
     * Check if a font is actually available
     */
    function isFontAvailable(fontName) {
        // Create a test element
        const testElement = document.createElement('div');
        testElement.style.fontFamily = fontName;
        testElement.style.fontSize = '16px';
        testElement.style.position = 'absolute';
        testElement.style.visibility = 'hidden';
        testElement.style.left = '-9999px';
        testElement.textContent = 'আবগদ'; // Bengali text
        
        document.body.appendChild(testElement);
        
        // Get computed style
        const computedStyle = window.getComputedStyle(testElement);
        const actualFont = computedStyle.fontFamily;
        
        // Clean up
        document.body.removeChild(testElement);
        
        // Check if the font is actually being used
        return actualFont.toLowerCase().includes(fontName.toLowerCase());
    }
    
    /**
     * Apply font to the page
     */
    function applyFont(fontStack) {
        const style = document.createElement('style');
        style.textContent = `
            :root {
                --primary-font: ${fontStack};
            }
            
            body, .bengali-text {
                font-family: var(--primary-font) !important;
            }
        `;
        document.head.appendChild(style);
        
        // Add class to body to indicate font status
        document.body.classList.remove('font-loading', 'font-error');
        document.body.classList.add('font-loaded');
        
        console.log(`🎨 Applied font stack: ${fontStack}`);
    }
    
    /**
     * Use system fallback fonts
     */
    function useSystemFallback() {
        const systemFontStack = SYSTEM_FONTS.concat(['sans-serif']).join(', ');
        applyFont(systemFontStack);
        
        document.body.classList.add('font-fallback');
        console.log('📱 Using system fallback fonts');
    }
    
    /**
     * Main font loading function
     */
    async function loadFonts() {
        // Add loading class
        document.body.classList.add('font-loading');
        
        try {
            // Try to load primary font first
            const primaryFont = FONT_SOURCES.find(f => f.type === 'primary');
            await loadFontCSS(primaryFont);
            
            // Wait a bit for font to be processed
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Check if font is actually available
            if (isFontAvailable(primaryFont.name)) {
                const fontStack = `'${primaryFont.name}', ${SYSTEM_FONTS.map(f => `'${f}'`).join(', ')}, sans-serif`;
                applyFont(fontStack);
                fontsLoaded = true;
                return;
            }
            
            // If primary font failed, try fallback
            const fallbackFont = FONT_SOURCES.find(f => f.type === 'fallback');
            await loadFontCSS(fallbackFont);
            
            // Wait a bit for font to be processed
            await new Promise(resolve => setTimeout(resolve, 500));
            
            if (isFontAvailable(fallbackFont.name)) {
                const fontStack = `'${fallbackFont.name}', ${SYSTEM_FONTS.map(f => `'${f}'`).join(', ')}, sans-serif`;
                applyFont(fontStack);
                fontsLoaded = true;
                return;
            }
            
            // If all external fonts failed, use system fonts
            throw new Error('All external fonts failed');
            
        } catch (error) {
            console.warn('Font loading failed:', error.message);
            
            // Retry if we haven't exceeded max retries
            if (retryCount < FONT_CONFIG.maxRetries) {
                retryCount++;
                console.log(`🔄 Retrying font loading (${retryCount}/${FONT_CONFIG.maxRetries})`);
                setTimeout(loadFonts, FONT_CONFIG.retryDelay);
                return;
            }
            
            // Use system fallback
            useSystemFallback();
        }
    }
    
    /**
     * Initialize font loading
     */
    function init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadFonts);
        } else {
            loadFonts();
        }
        
        // Also set up a fallback timer
        setTimeout(() => {
            if (!fontsLoaded) {
                console.warn('⚠️ Font loading taking too long, using system fallback');
                useSystemFallback();
            }
        }, FONT_CONFIG.timeout * 2);
    }
    
    // Start the font loading process
    init();
    
    // Export for debugging
    window.SimpleFontLoader = {
        loadFonts,
        useSystemFallback,
        isFontAvailable,
        getCurrentStatus: () => ({
            loaded: fontsLoaded,
            retries: retryCount,
            bodyClasses: Array.from(document.body.classList)
        })
    };
    
})();
