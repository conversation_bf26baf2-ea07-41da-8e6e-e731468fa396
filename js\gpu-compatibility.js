// GPU Compatibility Checker for Electron
// এই ফাইল GPU compatibility check করে এবং fallback provide করে

class GPUCompatibilityChecker {
    constructor() {
        this.isElectron = this.checkIfElectron();
        this.gpuInfo = null;
        this.isGPUSupported = false;
        this.fallbackMode = false;
        
        this.init();
    }

    checkIfElectron() {
        return typeof window !== 'undefined' &&
               typeof window.electronAPI === 'object' &&
               window.electronAPI !== null;
    }

    init() {
        console.log('🔍 GPU Compatibility Checker initialized');
        this.checkGPUSupport();
        this.setupFallbacks();
        this.monitorGPUHealth();
    }

    checkGPUSupport() {
        try {
            // Check WebGL support
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (gl) {
                this.isGPUSupported = true;
                this.gpuInfo = {
                    vendor: gl.getParameter(gl.VENDOR),
                    renderer: gl.getParameter(gl.RENDERER),
                    version: gl.getParameter(gl.VERSION),
                    shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION)
                };
                
                console.log('✅ GPU Support detected:', this.gpuInfo);
                this.enableGPUOptimizations();
            } else {
                console.warn('⚠️ No GPU support detected, using software rendering');
                this.enableSoftwareRendering();
            }
        } catch (error) {
            console.error('❌ GPU check failed:', error);
            this.enableSoftwareRendering();
        }
    }

    enableGPUOptimizations() {
        // Enable GPU-accelerated features
        const style = document.createElement('style');
        style.textContent = `
            .gpu-accelerated {
                will-change: transform;
                transform: translateZ(0);
                backface-visibility: hidden;
            }
            
            .chart-container canvas {
                will-change: transform;
                transform: translateZ(0);
            }
            
            .animation-container {
                will-change: transform;
                transform: translateZ(0);
            }
        `;
        document.head.appendChild(style);
        
        console.log('🚀 GPU optimizations enabled');
    }

    enableSoftwareRendering() {
        this.fallbackMode = true;
        
        // Disable GPU-intensive features
        const style = document.createElement('style');
        style.textContent = `
            /* Software rendering optimizations */
            .particle,
            .shimmer,
            .heavy-animation {
                animation: none !important;
                transform: none !important;
                will-change: auto !important;
            }
            
            .chart-container canvas {
                will-change: auto;
                transform: none;
            }
            
            /* Simplify animations for software rendering */
            * {
                transition-duration: 0.1s !important;
                animation-duration: 0.1s !important;
            }
            
            /* Disable expensive effects */
            .blur-effect,
            .backdrop-filter {
                backdrop-filter: none !important;
                filter: none !important;
            }
            
            /* Optimize shadows */
            .shadow,
            .box-shadow {
                box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
            }
        `;
        document.head.appendChild(style);
        
        console.log('💻 Software rendering mode enabled');
    }

    setupFallbacks() {
        // Setup fallback for failed GPU operations
        window.addEventListener('error', (event) => {
            if (event.message && event.message.includes('GPU')) {
                console.warn('⚠️ GPU error detected, switching to software rendering');
                this.enableSoftwareRendering();
            }
        });

        // Monitor for WebGL context loss
        window.addEventListener('webglcontextlost', (event) => {
            console.warn('⚠️ WebGL context lost, preventing default and switching to software rendering');
            event.preventDefault();
            this.enableSoftwareRendering();
        });

        // Monitor for WebGL context restoration
        window.addEventListener('webglcontextrestored', (event) => {
            console.log('✅ WebGL context restored');
            this.checkGPUSupport();
        });
    }

    monitorGPUHealth() {
        // Periodically check GPU health
        setInterval(() => {
            this.performGPUHealthCheck();
        }, 60000); // Every minute
    }

    performGPUHealthCheck() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl');
            
            if (!gl) {
                if (this.isGPUSupported && !this.fallbackMode) {
                    console.warn('⚠️ GPU support lost, switching to software rendering');
                    this.enableSoftwareRendering();
                }
                return;
            }

            // Test basic GPU operation
            const buffer = gl.createBuffer();
            if (!buffer) {
                console.warn('⚠️ GPU buffer creation failed');
                this.enableSoftwareRendering();
                return;
            }

            gl.deleteBuffer(buffer);
            
            // Check for GPU errors
            const error = gl.getError();
            if (error !== gl.NO_ERROR) {
                console.warn(`⚠️ GPU error detected: ${error}`);
                this.enableSoftwareRendering();
            }
        } catch (error) {
            console.warn('⚠️ GPU health check failed:', error);
            this.enableSoftwareRendering();
        }
    }

    // Get GPU information
    getGPUInfo() {
        return {
            isSupported: this.isGPUSupported,
            fallbackMode: this.fallbackMode,
            info: this.gpuInfo,
            recommendations: this.getRecommendations()
        };
    }

    getRecommendations() {
        const recommendations = [];

        if (!this.isGPUSupported) {
            recommendations.push('Update your graphics drivers');
            recommendations.push('Check if hardware acceleration is enabled in your browser');
            recommendations.push('Consider upgrading your graphics card');
        }

        if (this.fallbackMode) {
            recommendations.push('GPU acceleration disabled for stability');
            recommendations.push('Performance may be reduced');
            recommendations.push('Consider restarting the application');
        }

        return recommendations;
    }

    // Force software rendering mode
    forceSoftwareRendering() {
        console.log('🔧 Forcing software rendering mode');
        this.enableSoftwareRendering();
        
        // Notify user
        if (window.moneyManager && typeof window.moneyManager.showNotification === 'function') {
            window.moneyManager.showNotification({
                title: 'Performance Mode',
                message: 'Software rendering enabled for better compatibility',
                type: 'info'
            });
        }
    }

    // Try to re-enable GPU acceleration
    tryEnableGPU() {
        console.log('🔄 Attempting to re-enable GPU acceleration');
        this.fallbackMode = false;
        this.checkGPUSupport();
    }

    // Get performance recommendations
    getPerformanceRecommendations() {
        const recommendations = [];

        if (this.fallbackMode) {
            recommendations.push({
                title: 'Software Rendering Active',
                description: 'GPU acceleration is disabled. Performance may be reduced.',
                action: 'Try restarting the application or updating graphics drivers.',
                priority: 'medium'
            });
        }

        if (!this.isGPUSupported) {
            recommendations.push({
                title: 'No GPU Support',
                description: 'Hardware acceleration is not available.',
                action: 'Update graphics drivers or enable hardware acceleration in browser settings.',
                priority: 'high'
            });
        }

        if (this.gpuInfo && this.gpuInfo.renderer.includes('Software')) {
            recommendations.push({
                title: 'Software GPU Detected',
                description: 'Using software-based graphics rendering.',
                action: 'Install proper graphics drivers for better performance.',
                priority: 'high'
            });
        }

        return recommendations;
    }

    // Display GPU status widget
    showGPUStatus() {
        const widget = document.createElement('div');
        widget.id = 'gpu-status-widget';
        widget.style.cssText = `
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            min-width: 200px;
            cursor: pointer;
        `;

        this.updateGPUWidget(widget);
        document.body.appendChild(widget);

        // Click to toggle detailed view
        widget.addEventListener('click', () => {
            console.log('GPU Info:', this.getGPUInfo());
        });

        return widget;
    }

    updateGPUWidget(widget) {
        const status = this.isGPUSupported ? '✅ Enabled' : '❌ Disabled';
        const mode = this.fallbackMode ? 'Software' : 'Hardware';
        
        widget.innerHTML = `
            <div>GPU: ${status}</div>
            <div>Mode: ${mode}</div>
            ${this.gpuInfo ? `<div>Vendor: ${this.gpuInfo.vendor}</div>` : ''}
            <small>Click for details</small>
        `;

        // Color based on status
        const color = this.isGPUSupported && !this.fallbackMode ? '#4CAF50' : '#FF9800';
        widget.style.borderLeft = `4px solid ${color}`;
    }

    // Cleanup
    cleanup() {
        const widget = document.getElementById('gpu-status-widget');
        if (widget) {
            widget.remove();
        }
        console.log('🧹 GPU Compatibility Checker cleanup');
    }
}

// Initialize GPU Compatibility Checker
window.gpuCompatibilityChecker = new GPUCompatibilityChecker();

// Show GPU status in development
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    setTimeout(() => {
        window.gpuCompatibilityChecker.showGPUStatus();
    }, 3000);
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    window.gpuCompatibilityChecker.cleanup();
});
