// Performance Optimizer for My Finance App
// এই ফাইল অ্যাপের পারফরমেন্স উন্নত করার জন্য

class PerformanceOptimizer {
    constructor() {
        this.domCache = new Map();
        this.eventListeners = new Map();
        this.animationFrameId = null;
        this.throttleTimers = new Map();
        this.debounceTimers = new Map();
        this.observerInstances = new Map();
        
        this.init();
    }

    init() {
        console.log('🚀 Performance Optimizer initialized');
        this.setupDOMCache();
        this.optimizeEventListeners();
        this.optimizeAnimations();
        this.setupIntersectionObserver();
        this.optimizeChartRendering();
        this.setupMemoryCleanup();
    }

    // DOM Caching System
    setupDOMCache() {
        const commonSelectors = [
            'mainHeader', 'themeToggle', 'balanceToggle', 'fullscreenBtn',
            'notificationBell', 'globalSearchBtn', 'headerColorBtn',
            'dashboardGrid', 'recentTransactions', 'incomeForm', 'expenseForm',
            'editModal', 'colorPickerModal', 'notificationPanel'
        ];

        commonSelectors.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                this.domCache.set(id, element);
            }
        });

        console.log(`📦 DOM Cache: ${this.domCache.size} elements cached`);
    }

    // Get cached DOM element
    getElement(id) {
        if (this.domCache.has(id)) {
            return this.domCache.get(id);
        }
        
        const element = document.getElementById(id);
        if (element) {
            this.domCache.set(id, element);
        }
        return element;
    }

    // Optimized Event Listener Management
    optimizeEventListeners() {
        // Remove duplicate event listeners
        this.removeDuplicateListeners();
        
        // Use event delegation for dynamic content
        this.setupEventDelegation();
        
        // Throttle scroll and resize events
        this.throttleExpensiveEvents();
    }

    removeDuplicateListeners() {
        // Track and prevent duplicate listeners
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        const listenerMap = new WeakMap();

        EventTarget.prototype.addEventListener = function(type, listener, options) {
            if (!listenerMap.has(this)) {
                listenerMap.set(this, new Map());
            }
            
            const listeners = listenerMap.get(this);
            const key = `${type}_${listener.toString()}`;
            
            if (!listeners.has(key)) {
                listeners.set(key, true);
                originalAddEventListener.call(this, type, listener, options);
            }
        };
    }

    setupEventDelegation() {
        // Single event listener for all buttons
        document.addEventListener('click', this.handleDelegatedClick.bind(this), { passive: true });
        
        // Single event listener for all form inputs
        document.addEventListener('input', this.handleDelegatedInput.bind(this), { passive: true });
        
        console.log('🎯 Event delegation setup complete');
    }

    handleDelegatedClick(event) {
        const target = event.target.closest('[data-action]');
        if (!target) return;

        const action = target.dataset.action;
        const handler = this.getActionHandler(action);
        
        if (handler && typeof window.moneyManager[handler] === 'function') {
            event.preventDefault();
            window.moneyManager[handler](event);
        }
    }

    handleDelegatedInput(event) {
        const target = event.target;
        
        // Throttle input events for better performance
        if (target.type === 'text' || target.type === 'number') {
            this.throttle(`input_${target.id}`, () => {
                // Handle input validation or processing
                this.validateInput(target);
            }, 300);
        }
    }

    getActionHandler(action) {
        const actionMap = {
            'toggle-theme': 'toggleTheme',
            'toggle-balance': 'toggleBalance',
            'toggle-fullscreen': 'toggleFullscreen',
            'open-search': 'openGlobalSearch',
            'open-color-picker': 'openColorPicker'
        };
        
        return actionMap[action];
    }

    // Animation Optimization
    optimizeAnimations() {
        // Reduce motion for users who prefer it
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            this.disableAnimations();
            return;
        }

        // Use CSS containment for better performance
        this.setupCSSContainment();
        
        // Optimize heavy animations
        this.optimizeHeavyAnimations();
        
        console.log('🎬 Animations optimized');
    }

    disableAnimations() {
        const style = document.createElement('style');
        style.textContent = `
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        `;
        document.head.appendChild(style);
        console.log('🚫 Animations disabled for reduced motion');
    }

    setupCSSContainment() {
        const containmentElements = [
            '.dashboard-grid',
            '.recent-transactions-section',
            '.chart-card',
            '.notification-panel'
        ];

        containmentElements.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                el.style.contain = 'layout style paint';
            });
        });
    }

    optimizeHeavyAnimations() {
        // Replace heavy animations with lighter alternatives
        const heavyAnimations = document.querySelectorAll('.particle, .shimmer');
        heavyAnimations.forEach(el => {
            el.style.willChange = 'transform';
            el.style.transform = 'translateZ(0)'; // Force hardware acceleration
        });
    }

    // Throttle function for performance
    throttle(key, func, delay) {
        if (this.throttleTimers.has(key)) {
            return;
        }

        this.throttleTimers.set(key, true);
        setTimeout(() => {
            func();
            this.throttleTimers.delete(key);
        }, delay);
    }

    // Debounce function for performance
    debounce(key, func, delay) {
        if (this.debounceTimers.has(key)) {
            clearTimeout(this.debounceTimers.get(key));
        }

        const timer = setTimeout(() => {
            func();
            this.debounceTimers.delete(key);
        }, delay);

        this.debounceTimers.set(key, timer);
    }

    // Intersection Observer for lazy loading
    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadVisibleContent(entry.target);
                }
            });
        }, {
            rootMargin: '50px',
            threshold: 0.1
        });

        // Observe chart containers
        const chartContainers = document.querySelectorAll('.chart-container');
        chartContainers.forEach(container => {
            observer.observe(container);
        });

        this.observerInstances.set('charts', observer);
        console.log('👁️ Intersection Observer setup for lazy loading');
    }

    loadVisibleContent(element) {
        // Load charts only when visible
        if (element.classList.contains('chart-container')) {
            const canvas = element.querySelector('canvas');
            if (canvas && !canvas.dataset.loaded) {
                this.loadChart(canvas);
                canvas.dataset.loaded = 'true';
            }
        }
    }

    loadChart(canvas) {
        // Defer chart loading to next frame
        requestAnimationFrame(() => {
            if (window.moneyManager && typeof window.moneyManager.initializeChart === 'function') {
                window.moneyManager.initializeChart(canvas.id);
            }
        });
    }

    // Chart Rendering Optimization
    optimizeChartRendering() {
        // Batch chart updates
        this.chartUpdateQueue = [];
        this.isChartUpdateScheduled = false;
        
        console.log('📊 Chart rendering optimized');
    }

    queueChartUpdate(chartId, data) {
        this.chartUpdateQueue.push({ chartId, data });
        
        if (!this.isChartUpdateScheduled) {
            this.isChartUpdateScheduled = true;
            requestAnimationFrame(() => {
                this.processChartUpdates();
                this.isChartUpdateScheduled = false;
            });
        }
    }

    processChartUpdates() {
        const updates = [...this.chartUpdateQueue];
        this.chartUpdateQueue = [];
        
        updates.forEach(({ chartId, data }) => {
            this.updateChart(chartId, data);
        });
    }

    updateChart(chartId, data) {
        if (window.moneyManager && window.moneyManager.dashboardCharts) {
            const chart = window.moneyManager.dashboardCharts[chartId];
            if (chart) {
                chart.data = data;
                chart.update('none'); // No animation for better performance
            }
        }
    }

    // Memory Cleanup
    setupMemoryCleanup() {
        // Clean up every 5 minutes
        setInterval(() => {
            this.cleanupMemory();
        }, 5 * 60 * 1000);
        
        // Clean up on page unload
        window.addEventListener('beforeunload', () => {
            this.cleanupMemory();
        });
        
        console.log('🧹 Memory cleanup scheduled');
    }

    cleanupMemory() {
        // Clear unused DOM cache entries
        this.domCache.forEach((element, key) => {
            if (!document.contains(element)) {
                this.domCache.delete(key);
            }
        });

        // Clear completed timers
        this.throttleTimers.clear();
        this.debounceTimers.clear();

        // Force garbage collection if available
        if (window.gc) {
            window.gc();
        }

        console.log('🧹 Memory cleanup completed');
    }

    // Input validation optimization
    validateInput(input) {
        const value = input.value;
        const type = input.type;
        
        if (type === 'number') {
            const isValid = !isNaN(value) && value >= 0;
            input.classList.toggle('amount-input-valid', isValid);
            input.classList.toggle('amount-input-invalid', !isValid);
        }
    }

    // Performance monitoring
    startPerformanceMonitoring() {
        if ('performance' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach((entry) => {
                    if (entry.duration > 100) { // Log slow operations
                        console.warn(`⚠️ Slow operation detected: ${entry.name} took ${entry.duration}ms`);
                    }
                });
            });
            
            observer.observe({ entryTypes: ['measure', 'navigation'] });
            console.log('📈 Performance monitoring started');
        }
    }

    // Throttle expensive events
    throttleExpensiveEvents() {
        let scrollTimer;
        let resizeTimer;

        window.addEventListener('scroll', () => {
            if (scrollTimer) return;
            scrollTimer = setTimeout(() => {
                this.handleScroll();
                scrollTimer = null;
            }, 16); // ~60fps
        }, { passive: true });

        window.addEventListener('resize', () => {
            if (resizeTimer) return;
            resizeTimer = setTimeout(() => {
                this.handleResize();
                resizeTimer = null;
            }, 100);
        }, { passive: true });
    }

    handleScroll() {
        // Handle scroll events efficiently
        const scrollTop = window.pageYOffset;
        
        // Update header scroll effect
        const header = this.getElement('mainHeader');
        if (header) {
            header.classList.toggle('scrolled', scrollTop > 50);
        }
    }

    handleResize() {
        // Handle resize events efficiently
        if (window.moneyManager && typeof window.moneyManager.handleResize === 'function') {
            window.moneyManager.handleResize();
        }
    }
}

// Initialize Performance Optimizer
window.performanceOptimizer = new PerformanceOptimizer();
