// Optimized Script Loader for My Finance App
// এই ফাইল স্ক্রিপ্ট লোডিং অপটিমাইজ করার জন্য

class OptimizedLoader {
    constructor() {
        this.loadedScripts = new Set();
        this.loadingPromises = new Map();
        this.criticalResources = [
            'js/error-handler.js',
            'js/performance-optimizer.js',
            'js/simple-font-loader.js'
        ];
        this.deferredResources = [
            'https://unpkg.com/jodit@4.0.1/es2021/jodit.min.js',
            'js/new-notes.js',
            'js/electron-notification.js'
        ];
        
        this.init();
    }

    init() {
        console.log('📦 Optimized Loader initialized');
        this.preloadCriticalResources();
        this.setupLazyLoading();
        this.optimizeExternalResources();
    }

    // Preload critical resources
    async preloadCriticalResources() {
        const promises = this.criticalResources.map(src => this.loadScript(src, true));
        
        try {
            await Promise.all(promises);
            console.log('✅ Critical resources loaded');
            this.loadDeferredResources();
        } catch (error) {
            console.error('❌ Error loading critical resources:', error);
        }
    }

    // Load deferred resources after critical ones
    async loadDeferredResources() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            await new Promise(resolve => {
                document.addEventListener('DOMContentLoaded', resolve, { once: true });
            });
        }

        // Load deferred resources with delay
        setTimeout(() => {
            this.deferredResources.forEach(src => {
                this.loadScript(src, false);
            });
        }, 100);
    }

    // Load script with caching and error handling
    loadScript(src, critical = false) {
        if (this.loadedScripts.has(src)) {
            return Promise.resolve();
        }

        if (this.loadingPromises.has(src)) {
            return this.loadingPromises.get(src);
        }

        const promise = new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.async = !critical;
            script.defer = !critical;

            script.onload = () => {
                this.loadedScripts.add(src);
                this.loadingPromises.delete(src);
                console.log(`✅ Loaded: ${src}`);
                resolve();
            };

            script.onerror = () => {
                this.loadingPromises.delete(src);
                console.error(`❌ Failed to load: ${src}`);
                
                if (critical) {
                    reject(new Error(`Critical script failed to load: ${src}`));
                } else {
                    // Try fallback or continue without this script
                    this.handleScriptError(src);
                    resolve(); // Don't reject for non-critical scripts
                }
            };

            document.head.appendChild(script);
        });

        this.loadingPromises.set(src, promise);
        return promise;
    }

    // Handle script loading errors
    handleScriptError(src) {
        if (src.includes('jodit')) {
            console.warn('⚠️ Jodit Editor failed to load, using fallback');
            this.loadFallbackEditor();
        } else if (src.includes('chart.js')) {
            console.warn('⚠️ Chart.js failed to load, disabling charts');
            this.disableCharts();
        }
    }

    // Load fallback editor
    loadFallbackEditor() {
        // Create a simple textarea fallback
        const style = document.createElement('style');
        style.textContent = `
            .jodit-fallback {
                width: 100%;
                min-height: 300px;
                padding: 15px;
                border: 1px solid #ddd;
                border-radius: 8px;
                font-family: inherit;
                font-size: 14px;
                resize: vertical;
            }
        `;
        document.head.appendChild(style);

        // Replace Jodit instances with textarea
        setTimeout(() => {
            const joditContainers = document.querySelectorAll('[data-jodit]');
            joditContainers.forEach(container => {
                const textarea = document.createElement('textarea');
                textarea.className = 'jodit-fallback';
                textarea.placeholder = 'নোট লিখুন...';
                container.parentNode.replaceChild(textarea, container);
            });
        }, 1000);
    }

    // Disable charts if Chart.js fails
    disableCharts() {
        setTimeout(() => {
            const chartContainers = document.querySelectorAll('.chart-container');
            chartContainers.forEach(container => {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-chart-bar" style="font-size: 48px; margin-bottom: 15px;"></i>
                        <p>চার্ট লোড করা যায়নি</p>
                        <small>ইন্টারনেট সংযোগ চেক করুন</small>
                    </div>
                `;
            });
        }, 1000);
    }

    // Setup lazy loading for images and other resources
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            imageObserver.unobserve(img);
                        }
                    }
                });
            }, {
                rootMargin: '50px'
            });

            // Observe all images with data-src
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });

            console.log('👁️ Lazy loading setup for images');
        }
    }

    // Optimize external resources
    optimizeExternalResources() {
        // Preconnect to external domains
        this.preconnectToDomains([
            'https://fonts.googleapis.com',
            'https://fonts.gstatic.com',
            'https://cdnjs.cloudflare.com',
            'https://unpkg.com'
        ]);

        // Setup resource hints
        this.setupResourceHints();
    }

    // Preconnect to external domains
    preconnectToDomains(domains) {
        domains.forEach(domain => {
            const link = document.createElement('link');
            link.rel = 'preconnect';
            link.href = domain;
            link.crossOrigin = 'anonymous';
            document.head.appendChild(link);
        });

        console.log('🔗 Preconnected to external domains');
    }

    // Setup resource hints
    setupResourceHints() {
        // Prefetch important resources
        const prefetchResources = [
            'css/performance-optimized.css',
            'js/script.js'
        ];

        prefetchResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = resource;
            document.head.appendChild(link);
        });

        console.log('🔮 Resource hints setup');
    }

    // Check if script is loaded
    isScriptLoaded(src) {
        return this.loadedScripts.has(src);
    }

    // Wait for script to load
    waitForScript(src) {
        if (this.isScriptLoaded(src)) {
            return Promise.resolve();
        }

        if (this.loadingPromises.has(src)) {
            return this.loadingPromises.get(src);
        }

        return this.loadScript(src);
    }

    // Load CSS file dynamically
    loadCSS(href) {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;

            link.onload = () => {
                console.log(`✅ CSS Loaded: ${href}`);
                resolve();
            };

            link.onerror = () => {
                console.error(`❌ CSS Failed to load: ${href}`);
                reject(new Error(`CSS failed to load: ${href}`));
            };

            document.head.appendChild(link);
        });
    }

    // Optimize font loading
    optimizeFontLoading() {
        // Use font-display: swap for better performance
        const fontLinks = document.querySelectorAll('link[href*="fonts"]');
        fontLinks.forEach(link => {
            const url = new URL(link.href);
            if (!url.searchParams.has('display')) {
                url.searchParams.set('display', 'swap');
                link.href = url.toString();
            }
        });

        console.log('🔤 Font loading optimized');
    }

    // Monitor loading performance
    monitorPerformance() {
        if ('performance' in window && 'getEntriesByType' in performance) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    const resources = performance.getEntriesByType('resource');

                    console.log('📊 Performance Metrics:');
                    console.log(`DOM Content Loaded: ${navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart}ms`);
                    console.log(`Page Load: ${navigation.loadEventEnd - navigation.loadEventStart}ms`);
                    console.log(`Total Resources: ${resources.length}`);

                    // Log slow resources
                    const slowResources = resources.filter(resource => resource.duration > 1000);
                    if (slowResources.length > 0) {
                        console.warn('⚠️ Slow loading resources:', slowResources);
                    }
                }, 1000);
            });
        }
    }

    // Cleanup resources
    cleanup() {
        this.loadingPromises.clear();
        console.log('🧹 Optimized Loader cleanup completed');
    }
}

// Initialize Optimized Loader
window.optimizedLoader = new OptimizedLoader();

// Monitor performance
window.optimizedLoader.monitorPerformance();

// Optimize font loading
window.optimizedLoader.optimizeFontLoading();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    window.optimizedLoader.cleanup();
});
