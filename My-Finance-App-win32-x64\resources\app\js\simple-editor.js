/**
 * Simple Rich Text Editor
 * A lightweight alternative to Jodit Editor for Bengali text
 * No external dependencies, CSP-friendly
 */

class SimpleEditor {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            placeholder: 'এখানে লিখুন...',
            height: '300px',
            toolbar: ['bold', 'italic', 'underline', 'strikethrough', '|', 'ul', 'ol', '|', 'undo', 'redo', '|', 'clear'],
            ...options
        };
        
        this.history = [];
        this.historyIndex = -1;
        this.maxHistory = 50;
        
        this.init();
    }
    
    init() {
        if (!this.container) {
            console.error('Editor container not found');
            return;
        }
        
        this.createEditor();
        this.setupEventListeners();
        this.saveState();
    }
    
    createEditor() {
        // Create toolbar
        this.toolbar = document.createElement('div');
        this.toolbar.className = 'simple-editor-toolbar';
        this.toolbar.innerHTML = this.createToolbarHTML();
        
        // Create editor area
        this.editor = document.createElement('div');
        this.editor.className = 'simple-editor-content';
        this.editor.contentEditable = true;
        this.editor.style.height = this.options.height;
        this.editor.setAttribute('data-placeholder', this.options.placeholder);
        
        // Create container wrapper
        this.wrapper = document.createElement('div');
        this.wrapper.className = 'simple-editor-wrapper';
        this.wrapper.appendChild(this.toolbar);
        this.wrapper.appendChild(this.editor);
        
        // Replace container content
        this.container.innerHTML = '';
        this.container.appendChild(this.wrapper);
    }
    
    createToolbarHTML() {
        const buttons = {
            'bold': { icon: '𝐁', title: 'Bold (Ctrl+B)', command: 'bold' },
            'italic': { icon: '𝐼', title: 'Italic (Ctrl+I)', command: 'italic' },
            'underline': { icon: '𝐔', title: 'Underline (Ctrl+U)', command: 'underline' },
            'strikethrough': { icon: '𝐒', title: 'Strikethrough', command: 'strikeThrough' },
            'ul': { icon: '• List', title: 'Bullet List', command: 'insertUnorderedList' },
            'ol': { icon: '1. List', title: 'Numbered List', command: 'insertOrderedList' },
            'undo': { icon: '↶', title: 'Undo (Ctrl+Z)', command: 'undo' },
            'redo': { icon: '↷', title: 'Redo (Ctrl+Y)', command: 'redo' },
            'clear': { icon: '🗑', title: 'Clear All', command: 'clear' }
        };
        
        let html = '';
        
        this.options.toolbar.forEach(item => {
            if (item === '|') {
                html += '<span class="toolbar-separator">|</span>';
            } else if (buttons[item]) {
                const btn = buttons[item];
                html += `<button type="button" class="toolbar-btn" data-command="${btn.command}" title="${btn.title}">${btn.icon}</button>`;
            }
        });
        
        return html;
    }
    
    setupEventListeners() {
        // Toolbar button clicks
        this.toolbar.addEventListener('click', (e) => {
            if (e.target.classList.contains('toolbar-btn')) {
                e.preventDefault();
                const command = e.target.getAttribute('data-command');
                this.executeCommand(command);
            }
        });
        
        // Editor input events
        this.editor.addEventListener('input', () => {
            this.saveState();
            this.updatePlaceholder();
        });
        
        // Keyboard shortcuts
        this.editor.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key.toLowerCase()) {
                    case 'b':
                        e.preventDefault();
                        this.executeCommand('bold');
                        break;
                    case 'i':
                        e.preventDefault();
                        this.executeCommand('italic');
                        break;
                    case 'u':
                        e.preventDefault();
                        this.executeCommand('underline');
                        break;
                    case 'z':
                        if (e.shiftKey) {
                            e.preventDefault();
                            this.executeCommand('redo');
                        } else {
                            e.preventDefault();
                            this.executeCommand('undo');
                        }
                        break;
                    case 'y':
                        e.preventDefault();
                        this.executeCommand('redo');
                        break;
                }
            }
        });
        
        // Focus and blur events
        this.editor.addEventListener('focus', () => {
            this.wrapper.classList.add('focused');
        });
        
        this.editor.addEventListener('blur', () => {
            this.wrapper.classList.remove('focused');
        });
    }
    
    executeCommand(command) {
        this.editor.focus();
        
        switch (command) {
            case 'undo':
                this.undo();
                break;
            case 'redo':
                this.redo();
                break;
            case 'clear':
                if (confirm('সব টেক্সট মুছে ফেলবেন?')) {
                    this.editor.innerHTML = '';
                    this.saveState();
                    this.updatePlaceholder();
                }
                break;
            default:
                document.execCommand(command, false, null);
                this.saveState();
                break;
        }
        
        this.updatePlaceholder();
    }
    
    saveState() {
        const content = this.editor.innerHTML;
        
        // Don't save if content is the same as last state
        if (this.history[this.historyIndex] === content) {
            return;
        }
        
        // Remove any states after current index
        this.history = this.history.slice(0, this.historyIndex + 1);
        
        // Add new state
        this.history.push(content);
        this.historyIndex++;
        
        // Limit history size
        if (this.history.length > this.maxHistory) {
            this.history.shift();
            this.historyIndex--;
        }
    }
    
    undo() {
        if (this.historyIndex > 0) {
            this.historyIndex--;
            this.editor.innerHTML = this.history[this.historyIndex];
            this.updatePlaceholder();
        }
    }
    
    redo() {
        if (this.historyIndex < this.history.length - 1) {
            this.historyIndex++;
            this.editor.innerHTML = this.history[this.historyIndex];
            this.updatePlaceholder();
        }
    }
    
    updatePlaceholder() {
        const isEmpty = this.editor.textContent.trim() === '';
        this.editor.classList.toggle('empty', isEmpty);
    }
    
    // Public methods
    getContent() {
        return this.editor.innerHTML;
    }
    
    setContent(html) {
        this.editor.innerHTML = html;
        this.saveState();
        this.updatePlaceholder();
    }
    
    getText() {
        return this.editor.textContent;
    }
    
    clear() {
        this.editor.innerHTML = '';
        this.saveState();
        this.updatePlaceholder();
    }
    
    focus() {
        this.editor.focus();
    }
    
    blur() {
        this.editor.blur();
    }
    
    destroy() {
        if (this.wrapper && this.wrapper.parentNode) {
            this.wrapper.parentNode.removeChild(this.wrapper);
        }
    }
}

// Export for use
window.SimpleEditor = SimpleEditor;
