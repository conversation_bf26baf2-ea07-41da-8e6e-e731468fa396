// Electron Performance Optimizer
// এই ফাইল Electron অ্যাপের পারফরমেন্স উন্নত করার জন্য

class ElectronPerformanceOptimizer {
    constructor() {
        this.isElectron = this.checkIfElectron();
        this.electronAPI = this.isElectron ? window.electronAPI : null;
        this.processMetrics = {
            memory: { used: 0, total: 0 },
            cpu: { usage: 0 },
            gpu: { usage: 0 }
        };
        
        if (this.isElectron) {
            console.log('⚡ Electron Performance Optimizer initialized');
            this.init();
        } else {
            console.log('🌐 Running in browser - Electron optimizations skipped');
        }
    }

    checkIfElectron() {
        return typeof window !== 'undefined' &&
               typeof window.electronAPI === 'object' &&
               window.electronAPI !== null;
    }

    init() {
        this.optimizeElectronSettings();
        this.setupMemoryManagement();
        this.optimizeRendererProcess();
        this.setupIPCOptimization();
        this.enableHardwareAcceleration();
        this.setupProcessMonitoring();
        this.optimizeGarbageCollection();
    }

    // Optimize Electron-specific settings
    optimizeElectronSettings() {
        // Enable V8 optimizations
        if (typeof process !== 'undefined' && process.versions && process.versions.electron) {
            // Set V8 flags for better performance
            const v8Flags = [
                '--max-old-space-size=4096',  // Increase memory limit
                '--optimize-for-size',         // Optimize for smaller memory footprint
                '--gc-interval=100',          // More frequent garbage collection
                '--expose-gc'                 // Expose garbage collection
            ];

            console.log('🔧 V8 optimization flags applied');
        }

        // Optimize window settings
        this.optimizeWindowSettings();
    }

    optimizeWindowSettings() {
        // These optimizations should be applied in main.js
        const optimizedSettings = {
            webPreferences: {
                // Performance optimizations
                backgroundThrottling: false,    // Prevent throttling when window is hidden
                offscreen: false,              // Disable offscreen rendering for better performance
                
                // Memory optimizations
                nodeIntegrationInWorker: false, // Disable node in web workers
                nodeIntegrationInSubFrames: false, // Disable node in subframes
                
                // Security + Performance
                sandbox: false,                 // Keep disabled for performance (already secure with contextIsolation)
                contextIsolation: true,        // Keep enabled for security
                enableRemoteModule: false,     // Keep disabled
                webSecurity: true,             // Keep enabled
                
                // Additional performance settings
                experimentalFeatures: false,   // Disable experimental features
                plugins: false,                // Disable plugins
                java: false,                   // Disable Java
                webgl: true,                   // Enable WebGL for better graphics performance
                acceleratedCanvas: true,       // Enable hardware acceleration for canvas
                directWrite: true              // Enable DirectWrite on Windows
            },
            
            // Window performance settings
            show: false,                       // Don't show until ready
            paintWhenInitiallyHidden: false,   // Don't paint when hidden
            thickFrame: false,                 // Reduce frame thickness for performance
            enableLargerThanScreen: false      // Prevent oversized windows
        };

        console.log('🪟 Window optimization settings prepared');
        return optimizedSettings;
    }

    // Setup memory management
    setupMemoryManagement() {
        // Monitor memory usage
        setInterval(() => {
            this.checkMemoryUsage();
        }, 30000); // Check every 30 seconds

        // Force garbage collection periodically
        setInterval(() => {
            this.forceGarbageCollection();
        }, 120000); // Every 2 minutes

        // Clear caches periodically
        setInterval(() => {
            this.clearCaches();
        }, 300000); // Every 5 minutes

        console.log('💾 Memory management setup complete');
    }

    checkMemoryUsage() {
        if (typeof process !== 'undefined' && process.memoryUsage) {
            const memUsage = process.memoryUsage();
            this.processMetrics.memory = {
                used: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
                total: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
                external: Math.round(memUsage.external / 1024 / 1024), // MB
                rss: Math.round(memUsage.rss / 1024 / 1024) // MB
            };

            // Warn if memory usage is high
            if (this.processMetrics.memory.used > 200) { // > 200MB
                console.warn(`⚠️ High memory usage: ${this.processMetrics.memory.used}MB`);
                this.forceGarbageCollection();
            }
        }
    }

    forceGarbageCollection() {
        if (typeof global !== 'undefined' && global.gc) {
            global.gc();
            console.log('🗑️ Garbage collection forced');
        } else if (typeof window !== 'undefined' && window.gc) {
            window.gc();
            console.log('🗑️ Garbage collection forced');
        }
    }

    clearCaches() {
        // Clear various caches
        if (this.electronAPI) {
            // Clear session cache through IPC if needed
            console.log('🧹 Clearing caches...');
        }

        // Clear DOM caches
        if (window.performanceOptimizer) {
            window.performanceOptimizer.cleanupMemory();
        }

        // Clear image caches
        this.clearImageCaches();
    }

    clearImageCaches() {
        // Remove unused images from memory
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!this.isElementVisible(img)) {
                // Temporarily clear src to free memory
                const originalSrc = img.src;
                img.src = '';
                setTimeout(() => {
                    img.src = originalSrc;
                }, 100);
            }
        });
    }

    isElementVisible(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    // Optimize renderer process
    optimizeRendererProcess() {
        // Disable unnecessary features
        this.disableUnnecessaryFeatures();
        
        // Optimize DOM operations
        this.optimizeDOMOperations();
        
        // Setup efficient event handling
        this.setupEfficientEventHandling();
        
        console.log('🎨 Renderer process optimized');
    }

    disableUnnecessaryFeatures() {
        // Disable smooth scrolling for better performance
        document.documentElement.style.scrollBehavior = 'auto';
        
        // Disable text selection where not needed
        const nonSelectableElements = document.querySelectorAll('.btn, .header, .sidebar');
        nonSelectableElements.forEach(el => {
            el.style.userSelect = 'none';
            el.style.webkitUserSelect = 'none';
        });
    }

    optimizeDOMOperations() {
        // Batch DOM updates
        this.domUpdateQueue = [];
        this.isDOMUpdateScheduled = false;
        
        // Override common DOM methods for batching
        this.setupDOMBatching();
    }

    setupDOMBatching() {
        // Create a batched update system
        this.batchDOMUpdate = (callback) => {
            this.domUpdateQueue.push(callback);
            
            if (!this.isDOMUpdateScheduled) {
                this.isDOMUpdateScheduled = true;
                requestAnimationFrame(() => {
                    this.processDOMUpdates();
                    this.isDOMUpdateScheduled = false;
                });
            }
        };
    }

    processDOMUpdates() {
        const updates = [...this.domUpdateQueue];
        this.domUpdateQueue = [];
        
        // Process all updates in a single frame
        updates.forEach(callback => {
            try {
                callback();
            } catch (error) {
                console.error('DOM update error:', error);
            }
        });
    }

    setupEfficientEventHandling() {
        // Use passive event listeners where possible
        const passiveEvents = ['scroll', 'wheel', 'touchstart', 'touchmove'];
        
        passiveEvents.forEach(eventType => {
            document.addEventListener(eventType, () => {}, { passive: true });
        });
        
        // Throttle resize events
        let resizeTimeout;
        window.addEventListener('resize', () => {
            if (resizeTimeout) return;
            resizeTimeout = setTimeout(() => {
                this.handleResize();
                resizeTimeout = null;
            }, 100);
        });
    }

    handleResize() {
        // Efficient resize handling
        this.batchDOMUpdate(() => {
            // Update layouts efficiently
            if (window.moneyManager && typeof window.moneyManager.handleResize === 'function') {
                window.moneyManager.handleResize();
            }
        });
    }

    // Setup IPC optimization
    setupIPCOptimization() {
        if (!this.electronAPI) return;
        
        // Batch IPC calls
        this.ipcQueue = [];
        this.isIPCScheduled = false;
        
        // Create optimized IPC wrapper
        this.optimizedIPC = this.createOptimizedIPC();
        
        console.log('📡 IPC optimization setup complete');
    }

    createOptimizedIPC() {
        return {
            // Batched invoke
            invoke: (channel, ...args) => {
                return new Promise((resolve, reject) => {
                    this.ipcQueue.push({ channel, args, resolve, reject });
                    this.scheduleIPCBatch();
                });
            },
            
            // Direct invoke for critical operations
            invokeImmediate: (channel, ...args) => {
                return this.electronAPI.invoke(channel, ...args);
            }
        };
    }

    scheduleIPCBatch() {
        if (this.isIPCScheduled) return;
        
        this.isIPCScheduled = true;
        setTimeout(() => {
            this.processIPCBatch();
            this.isIPCScheduled = false;
        }, 10); // Small delay to batch multiple calls
    }

    async processIPCBatch() {
        const batch = [...this.ipcQueue];
        this.ipcQueue = [];
        
        // Process IPC calls
        for (const { channel, args, resolve, reject } of batch) {
            try {
                const result = await this.electronAPI.invoke(channel, ...args);
                resolve(result);
            } catch (error) {
                reject(error);
            }
        }
    }

    // Enable hardware acceleration
    enableHardwareAcceleration() {
        // Force hardware acceleration for specific elements
        const acceleratedElements = [
            '.chart-container canvas',
            '.animation-container',
            '.particle',
            '.shimmer'
        ];
        
        acceleratedElements.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                el.style.willChange = 'transform';
                el.style.transform = 'translateZ(0)';
                el.style.backfaceVisibility = 'hidden';
            });
        });
        
        console.log('🚀 Hardware acceleration enabled');
    }

    // Setup process monitoring
    setupProcessMonitoring() {
        // Monitor process performance
        setInterval(() => {
            this.monitorProcessPerformance();
        }, 60000); // Every minute
        
        console.log('📊 Process monitoring setup complete');
    }

    monitorProcessPerformance() {
        // Check CPU usage (if available)
        if (typeof process !== 'undefined' && process.cpuUsage) {
            const cpuUsage = process.cpuUsage();
            this.processMetrics.cpu = {
                user: cpuUsage.user,
                system: cpuUsage.system
            };
        }
        
        // Log performance metrics
        console.log('📈 Process Metrics:', this.processMetrics);
    }

    // Optimize garbage collection
    optimizeGarbageCollection() {
        // Set up intelligent garbage collection
        let lastGCTime = Date.now();
        
        setInterval(() => {
            const now = Date.now();
            const timeSinceLastGC = now - lastGCTime;
            
            // Force GC if memory usage is high or enough time has passed
            if (this.processMetrics.memory.used > 150 || timeSinceLastGC > 300000) {
                this.forceGarbageCollection();
                lastGCTime = now;
            }
        }, 60000); // Check every minute
        
        console.log('🗑️ Intelligent garbage collection setup');
    }

    // Get performance metrics
    getPerformanceMetrics() {
        return {
            ...this.processMetrics,
            isElectron: this.isElectron,
            timestamp: Date.now()
        };
    }

    // Cleanup
    cleanup() {
        console.log('🧹 Electron Performance Optimizer cleanup');
    }
}

// Initialize Electron Performance Optimizer
window.electronPerformanceOptimizer = new ElectronPerformanceOptimizer();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    window.electronPerformanceOptimizer.cleanup();
});
