@echo off
echo ========================================
echo    My Finance App - Safe Mode Start
echo ========================================
echo.

echo 🛡️ Starting in safe mode (software rendering)...
echo This mode disables GPU acceleration to prevent crashes.
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Node.js is not installed!
    echo Please install Node.js from https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js found
echo.

REM Check if dependencies are installed
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
    if errorlevel 1 (
        echo ❌ Error: Failed to install dependencies!
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed
    echo.
)

echo 🚀 Starting Electron app in safe mode...
echo.

REM Start with GPU disabled for compatibility
electron . --dev --disable-gpu --disable-gpu-sandbox --no-sandbox --disable-software-rasterizer

echo.
echo 📱 App closed.
echo.
pause
