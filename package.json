{"name": "my-finance-app", "version": "1.0.0", "description": "My Finance App - একটি বাংলা ফিন্যান্স ম্যানেজমেন্ট অ্যাপ", "main": "js/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "perf": "electron . --dev --trace-gc --prof", "perf-memory": "electron . --dev --trace-gc --expose-gc", "perf-cpu": "electron . --dev --prof --log-timer-events", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "dist": "electron-builder --publish=never"}, "keywords": ["finance", "money", "budget", "bangla", "electron", "desktop"], "author": "<PERSON> <PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"electron": "^27.3.11", "electron-builder": "^24.6.4", "electron-packager": "^17.1.2"}, "build": {"appId": "com.fahimhaque.myfinanceapp", "productName": "My Finance App", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "!dist/**/*", "!.git/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}